#!/usr/bin/env python3
"""
face_recog_rt.py

Real-time face recognition for Raspberry Pi + Hailo/DeGirum + ChromaDB.

Modes:
  --mode ui       : show live video with bounding boxes/labels
  --mode console  : headless, record video for --duration seconds to --output

Camera backends:
  --camera auto   : try Picamera2, else fallback to OpenCV
  --camera picam  : force Picamera2
  --camera cv     : force OpenCV VideoCapture(0)

Depends:
  - degirum (DeGirum)
  - chromadb
  - opencv-python
  - picamera2 (optional for Pi Camera)
"""
from __future__ import annotations
import os
import time
import argparse
import logging
import signal
import threading
import queue
from typing import Optional, List, Tuple, Dict, Any

import numpy as np
import cv2

# optional libs
try:
    import degirum as dg
except Exception:
    dg = None

try:
    import chromadb
except Exception:
    chromadb = None

# try picamera2 import - optional
try:
    from picamera2 import Picamera2, Preview
    PICAMERA2_AVAIL = True
except Exception:
    PICAMERA2_AVAIL = False

# logging
logging.basicConfig(level=logging.INFO, format="[%(levelname)s] %(message)s")
logger = logging.getLogger("face_recog_rt")

# constants
REF_KPS_112 = np.array([
    [38.2946, 51.6963],
    [73.5318, 51.5014],
    [56.0252, 71.7366],
    [41.5493, 92.3655],
    [70.7299, 92.2041]
], dtype=np.float32)

# default models (from your code)
DETECTOR_MODEL = "retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1"
EMBED_MODEL = "arcface_mobilefacenet--112x112_quant_hailort_hailo8l_1"

# === utilities ===
def expand(p: Optional[str]) -> Optional[str]:
    return os.path.expanduser(p) if p else p

def safe_get_results(pred_out) -> List[dict]:
    if pred_out is None:
        return []
    if hasattr(pred_out, "results"):
        return list(getattr(pred_out, "results") or [])
    if isinstance(pred_out, (list, tuple)):
        return list(pred_out)
    if isinstance(pred_out, dict):
        return [pred_out]
    return []

def safe_extract_embedding(result: Dict[str, Any]) -> Optional[np.ndarray]:
    candidates = ['embedding', 'embeddings', 'vector', 'feat', 'features', 'features_vector']
    for k in result:
        if k.lower() in candidates:
            try:
                arr = np.asarray(result[k], dtype=float).flatten()
                if arr.size:
                    return arr
            except Exception:
                pass
    # fallback
    for k, v in result.items():
        try:
            arr = np.asarray(v, dtype=float).flatten()
            if arr.size:
                return arr
        except Exception:
            continue
    return None

def safe_parse_landmarks(raw_lm, w: int, h: int) -> Optional[np.ndarray]:
    if raw_lm is None:
        return None
    try:
        arr = np.asarray(raw_lm, dtype=float).flatten()
    except Exception:
        try:
            flat = []
            for item in raw_lm:
                flat.extend(np.asarray(item).flatten().tolist())
            arr = np.asarray(flat, dtype=float)
        except Exception:
            return None
    if arr.size == 10:
        arr = arr.reshape(5,2)
    else:
        try:
            arr = arr.reshape(5,2)
        except Exception:
            return None
    if arr.max() <= 1.01:
        arr[:,0] *= w
        arr[:,1] *= h
    return arr.astype(np.float32)

def align_face_to_112(frame_rgb: np.ndarray, landmarks: Optional[np.ndarray], bbox: Optional[List[int]]) -> np.ndarray:
    H,W = frame_rgb.shape[:2]
    if landmarks is not None:
        try:
            M, _ = cv2.estimateAffinePartial2D(np.array(landmarks, dtype=np.float32), REF_KPS_112, method=cv2.RANSAC, ransacReprojThreshold=5.0)
            if M is not None:
                return cv2.warpAffine(frame_rgb, M, (112,112), flags=cv2.INTER_LINEAR, borderMode=cv2.BORDER_REPLICATE)
        except Exception:
            pass
    if bbox is not None:
        try:
            x1,y1,x2,y2 = bbox
            x1 = max(0, int(round(x1))); y1 = max(0, int(round(y1)))
            x2 = min(W-1, int(round(x2))); y2 = min(H-1, int(round(y2)))
            if x2 > x1 and y2 > y1:
                margin = int(round(0.2 * max(x2-x1, y2-y1)))
                cx1 = max(0, x1 - margin); cy1 = max(0, y1 - margin)
                cx2 = min(W-1, x2 + margin); cy2 = min(H-1, y2 + margin)
                crop = frame_rgb[cy1:cy2, cx1:cx2]
                if crop.size != 0:
                    return cv2.resize(crop, (112,112), interpolation=cv2.INTER_LINEAR)
        except Exception:
            pass
    # center-crop fallback
    hmin = min(H,W); cy=H//2; cx=W//2; half=hmin//2
    crop = frame_rgb[max(0,cy-half):min(H,cy+half), max(0,cx-half):min(W,cx+half)]
    if crop.size == 0:
        return cv2.resize(frame_rgb, (112,112), interpolation=cv2.INTER_LINEAR)
    return cv2.resize(crop, (112,112), interpolation=cv2.INTER_LINEAR)

# === Chroma helper: try query, fallback to centroid scan ===
def open_chroma_client(db_path: str):
    if chromadb is None:
        raise RuntimeError("chromadb package not found. Install chromadb.")
    p = expand(db_path) or "db/face_embeddings_db"
    os.makedirs(p, exist_ok=True)
    client = chromadb.PersistentClient(path=p)
    try:
        col = client.get_or_create_collection(name="face_embeddings", metadata={"hnsw:space":"cosine"})
    except Exception:
        try:
            col = client.get_collection("face_embeddings")
        except Exception:
            col = client.create_collection("face_embeddings")
    return client, col

# try to query using collection.query, else fetch centroids manually
def db_query_topk(collection, embedding: List[float], k: int=3):
    """Return list of (metadata, distance) up to k results.
       Attempts collection.query(...) first, falls back to centroid scan.
    """
    # try query API
    try:
        resp = collection.query(query_embeddings=[embedding], n_results=k)
        # API shapes vary. Try common ones:
        metadatas = resp.get("metadatas") if isinstance(resp, dict) else None
        distances = resp.get("distances") if isinstance(resp, dict) else None
        if metadatas is not None and distances is not None:
            out = []
            for mlist, dlist in zip(metadatas, distances):
                # mlist is list of metas for one query
                for m,d in zip(mlist, dlist):
                    out.append((m,d))
            return out[:k]
    except Exception:
        pass

    # fallback: load centroids and compute cosine distance
    try:
        resp = collection.get(where={"type":"centroid"})
        metas = resp.get("metadatas", []) or []
        vecs = resp.get("embeddings", []) or []
        out = []
        qv = np.asarray(embedding, dtype=float)
        qv = qv / (np.linalg.norm(qv) + 1e-12)
        for m,v in zip(metas, vecs):
            vv = np.asarray(v, dtype=float)
            vv = vv / (np.linalg.norm(vv) + 1e-12)
            # cosine distance = 1 - dot
            d = 1.0 - float(np.dot(qv, vv))
            out.append((m,d))
        out.sort(key=lambda x: x[1])
        return out[:k]
    except Exception:
        return []

# === FaceRecognizer class ===
class FaceRecognizer:
    def __init__(self, zoo_path: str, detector_name: str = DETECTOR_MODEL, embed_name: str = EMBED_MODEL, db_path: str = "db/face_embeddings_db", top_k: int = 3, min_score: float = 0.5):
        self.zoo_path = expand(zoo_path)
        if dg is None:
            raise RuntimeError("degirum (dg) not available; install DeGirum SDK to use Hailo models")
        self.zoo = dg.connect(dg.LOCAL, self.zoo_path)
        self.detector = self.zoo.load_model(detector_name)
        self.embedder = self.zoo.load_model(embed_name)
        # try set RGB
        try:
            self.detector.input_numpy_colorspace = "RGB"
        except Exception:
            pass
        try:
            self.embedder.input_numpy_colorspace = "RGB"
        except Exception:
            pass
        self.client, self.collection = open_chroma_client(db_path)
        self.top_k = top_k
        self.min_score = min_score

    def detect_and_embed(self, frame_rgb: np.ndarray) -> List[dict]:
        """
        Run detector and embed on frame_rgb.
        Returns list of dicts: {bbox: [x1,y1,x2,y2], score: float, emb: np.ndarray, meta: optional}
        """
        out = []
        try:
            det_out = self.detector.predict(frame_rgb)
        except Exception as e:
            logger.warning("Detector predict failed: %s", e)
            return out
        results = safe_get_results(det_out)
        if not results:
            return out
        # filter by score, produce embeddings
        for res in results:
            score = float(res.get("score", 0.0))
            if score < self.min_score:
                continue
            raw_bbox = res.get("bbox") or res.get("box") or res.get("rectangle")
            bbox = None
            try:
                if raw_bbox is not None:
                    a = np.asarray(raw_bbox, dtype=float).flatten()
                    if a.size == 4:
                        x1,y1,x2,y2 = a.tolist()
                        if (x2 - x1) <= 1.0: x2 = x1 + x2
                        if (y2 - y1) <= 1.0: y2 = y1 + y2
                        bbox = [int(round(x1)), int(round(y1)), int(round(x2)), int(round(y2))]
            except Exception:
                bbox = None
            lm = safe_parse_landmarks(res.get("landmarks") or res.get("landmark") or res.get("keypoints"), frame_rgb.shape[1], frame_rgb.shape[0])
            aligned = align_face_to_112(frame_rgb, lm, bbox)
            try:
                emb_out = self.embedder.predict(aligned)
            except Exception as e:
                logger.debug("Embedder predict failed: %s", e)
                continue
            emb_res = safe_get_results(emb_out)
            if not emb_res:
                continue
            emb_vec = safe_extract_embedding(emb_res[0])
            if emb_vec is None:
                continue
            emb = np.asarray(emb_vec, dtype=float).flatten()
            nrm = np.linalg.norm(emb)
            if nrm <= 1e-8:
                continue
            emb = (emb / nrm).astype(float)
            out.append({"bbox": bbox, "score": score, "emb": emb})
        return out

    def recognize_embeddings(self, emb: List[float]) -> List[Tuple[dict,float]]:
        """Query DB for top-k matches. Returns list of (metadata, distance)."""
        try:
            results = db_query_topk(self.collection, emb.tolist() if isinstance(emb, np.ndarray) else emb, k=self.top_k)
            return results
        except Exception as e:
            logger.debug("DB query failed: %s", e)
            return []

# === Camera wrappers ===
class CameraCapture:
    def __init__(self, backend: str = "auto", width: int = 640, height: int = 480, fps: int = 30):
        self.backend = backend
        self.width = width; self.height = height; self.fps = fps
        self.picam = None
        self.cap = None
        self.running = False
        self.use_picam2 = False
        if backend == "auto" and PICAMERA2_AVAIL:
            self.use_picam2 = True
        elif backend == "picam" and PICAMERA2_AVAIL:
            self.use_picam2 = True

    def start(self):
        if self.use_picam2:
            logger.info("Using Picamera2 backend")
            self.picam = Picamera2()
            config = self.picam.create_preview_configuration(main={"format":"RGB888","size":(self.width,self.height)})
            self.picam.configure(config)
            self.picam.start()
            self.running = True
        else:
            logger.info("Using OpenCV VideoCapture backend")
            self.cap = cv2.VideoCapture(0)
            # try set resolution
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.width)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.height)
            self.cap.set(cv2.CAP_PROP_FPS, self.fps)
            self.running = True

    def read(self) -> Optional[np.ndarray]:
        if not self.running:
            return None
        if self.use_picam2:
            frm = self.picam.capture_array()
            # Picamera2 returns RGB already
            return frm
        else:
            ok, frm = self.cap.read()
            if not ok:
                return None
            # convert BGR->RGB
            return cv2.cvtColor(frm, cv2.COLOR_BGR2RGB)

    def stop(self):
        if self.use_picam2 and self.picam:
            try:
                self.picam.stop()
            except Exception:
                pass
            self.picam = None
        if self.cap:
            try:
                self.cap.release()
            except Exception:
                pass
            self.cap = None
        self.running = False

# === threaded pipeline ===
def worker_loop(recognizer: FaceRecognizer, in_q: "queue.Queue[Tuple[int, np.ndarray]]", out_q: "queue.Queue[Tuple[int, np.ndarray]]", stop_evt: threading.Event):
    """
    Worker takes (frame_id, rgb_frame) from in_q, runs detect+embed+recognize,
    draws boxes/labels on a copy and puts (frame_id, annotated_rgb) into out_q.
    """
    while not stop_evt.is_set():
        try:
            item = in_q.get(timeout=0.2)
        except queue.Empty:
            continue
        fid, frame_rgb = item
        # run detect+embed
        detections = recognizer.detect_and_embed(frame_rgb)
        annotated = frame_rgb.copy()
        # draw boxes + labels
        for det in detections:
            bbox = det.get("bbox")
            score = det.get("score", 0.0)
            emb = det.get("emb")
            label = "unknown"
            dist = None
            if emb is not None:
                matches = recognizer.recognize_embeddings(emb)
                if matches:
                    top_meta, top_d = matches[0]
                    # meta may be dict or list; handle dict
                    if isinstance(top_meta, dict):
                        name = top_meta.get("person") or top_meta.get("label") or "unknown"
                    else:
                        # sometimes metadata wrapped in list
                        try:
                            name = top_meta[0].get("person") or "unknown"
                        except Exception:
                            name = "unknown"
                    label = f"{name}"
                    dist = top_d
            # draw box
            if bbox:
                x1,y1,x2,y2 = bbox
                # convert rgb to bgr for OpenCV drawing - later we return rgb, but cv2 needs BGR for display when converting back
                cv2.rectangle(annotated, (x1,y1), (x2,y2), (0,255,0), 2)
                text = f"{label} {score:.2f}" if dist is None else f"{label} {dist:.3f}"
                cv2.putText(annotated, text, (x1, max(0,y1-6)), cv2.FONT_HERSHEY_SIMPLEX, 0.45, (255,255,255), 1, cv2.LINE_AA)
        # push annotated
        try:
            out_q.put((fid, annotated), timeout=0.5)
        except queue.Full:
            pass
        in_q.task_done()
    logger.info("Worker exiting")

# === main app ===
def main(args):
    # validate
    if args.mode not in ("ui","console"):
        raise SystemExit("mode must be ui or console")
    # camera
    cam_backend = args.camera
    if cam_backend == "auto":
        cam_backend = "picam" if PICAMERA2_AVAIL else "cv"
    cam_backend = "picam" if cam_backend in ("picam","picamera2") else "cv"

    cap = CameraCapture(backend=cam_backend, width=args.width, height=args.height, fps=args.fps)
    cap.start()

    recognizer = FaceRecognizer(zoo_path=args.zoo_path, top_k=args.top_k, min_score=args.min_score, db_path=args.db_path)

    in_q: "queue.Queue[Tuple[int, np.ndarray]]" = queue.Queue(maxsize=4)
    out_q: "queue.Queue[Tuple[int, np.ndarray]]" = queue.Queue(maxsize=4)
    stop_evt = threading.Event()
    worker = threading.Thread(target=worker_loop, args=(recognizer, in_q, out_q, stop_evt), daemon=True)
    worker.start()

    # video writer if recording
    writer = None
    if args.output:
        fourcc = cv2.VideoWriter_fourcc(*"mp4v")
        writer = cv2.VideoWriter(args.output, fourcc, args.fps, (args.width, args.height))

    frame_id = 0
    last_print = time.time()
    frames_processed = 0
    start_time = time.time()

    def handle_sigint(signum, frame):
        logger.info("Signal received, shutting down...")
        stop_evt.set()
    signal.signal(signal.SIGINT, handle_sigint)

    # main loop
    try:
        while True:
            if args.mode == "console" and args.duration:
                if time.time() - start_time >= args.duration:
                    logger.info("Duration reached, stopping.")
                    break
            frm = cap.read()
            if frm is None:
                time.sleep(0.01)
                continue
            # push to worker if queue free
            try:
                in_q.put((frame_id, frm.copy()), timeout=0.01)
            except queue.Full:
                # drop frame
                pass

            # try to fetch annotated for display/record
            try:
                fid_out, ann = out_q.get_nowait()
                # convert RGB->BGR for display/writing
                bgr = cv2.cvtColor(ann, cv2.COLOR_RGB2BGR)
                if args.mode == "ui":
                    cv2.imshow("FaceRec", bgr)
                    key = cv2.waitKey(1) & 0xFF
                    if key == ord("q"):
                        logger.info("Quit pressed")
                        break
                if writer:
                    writer.write(bgr)
                frames_processed += 1
            except queue.Empty:
                pass

            frame_id += 1
            # periodic stats
            if time.time() - last_print >= 5.0:
                elapsed = time.time() - start_time
                fps = frames_processed / elapsed if elapsed > 0 else 0.0
                logger.info("Elapsed %.1fs, processed frames: %d, approx FPS: %.2f", elapsed, frames_processed, fps)
                last_print = time.time()

            # small sleep to be cooperative
            time.sleep(0.001)
    finally:
        stop_evt.set()
        worker.join(timeout=1.0)
        cap.stop()
        if writer:
            writer.release()
        try:
            cv2.destroyAllWindows()
        except Exception:
            pass
        logger.info("Shutdown complete")

# === CLI ===
if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--mode", choices=["ui","console"], default="console")
    parser.add_argument("--camera", choices=["auto","picam","cv"], default="picam")
    parser.add_argument("--zoo_path", type=str, default="~/degirum-zoo")
    parser.add_argument("--db_path", type=str, default="db/face_embeddings_db")
    parser.add_argument("--output", type=str, default="output.mp4", help="Output mp4 file (console or ui)")
    parser.add_argument("--duration", type=int, default=60, help="Duration in seconds for console mode")
    parser.add_argument("--width", type=int, default=640)
    parser.add_argument("--height", type=int, default=480)
    parser.add_argument("--fps", type=int, default=20)
    parser.add_argument("--top_k", type=int, default=3)
    parser.add_argument("--min_score", type=float, default=0.5)
    args = parser.parse_args()

    main(args)
